# 基线核查工具开发需求文档

## 1. 项目概述

### 1.1 项目背景
基于Golin项目的主机核查模块，开发一款专业的基线核查工具，用于企业网络安全等级保护和安全配置审计。

### 1.2 项目目标
- 开发跨平台桌面应用程序
- 实现主机安全配置自动化核查
- 支持等保三级标准要求
- 生成专业的PDF核查报告

### 1.3 技术选型
- **后端框架**: Go + Wails v2
- **前端**: 原生桌面界面（Wails内置）
- **数据库**: SQLite（本地存储）
- **报告生成**: PDF导出
- **支持平台**: Windows、Linux、macOS

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 主机管理模块
- **主机信息管理**
  - 添加/编辑/删除主机
  - 主机基本信息（IP、主机名、操作系统、描述）
  - 连接配置（SSH、本地连接）
  - 主机状态监控

- **主机组管理**
  - 创建/编辑/删除主机组
  - 主机组织结构管理
  - 批量主机操作
  - 主机组权限管理

#### 2.1.2 核查模块
- **Linux系统核查**
  - 用户账户安全配置
  - 密码策略检查
  - 文件权限审计
  - 网络服务配置
  - 系统日志配置
  - 防火墙规则检查

- **Windows系统核查**
  - 本地安全策略
  - 用户权限分配
  - 审计策略配置
  - 服务配置检查
  - 注册表安全设置

- **数据库核查**
  - MySQL安全配置
  - PostgreSQL安全配置
  - Oracle安全配置
  - Redis安全配置
  - SQL Server安全配置

#### 2.1.3 报告模块
- **PDF报告生成**
  - 核查结果汇总
  - 风险等级分类
  - 整改建议
  - 图表统计
  - 自定义报告模板

### 2.2 辅助功能
- 核查任务调度
- 历史记录查询
- 配置模板管理
- 用户权限管理

## 3. 技术架构设计

### 3.1 整体架构
```
┌─────────────────────────────────────┐
│           前端界面层                 │
│    (Wails原生桌面界面)               │
├─────────────────────────────────────┤
│           业务逻辑层                 │
│  ┌─────────┬─────────┬─────────┐    │
│  │主机管理 │核查引擎 │报告生成 │    │
│  └─────────┴─────────┴─────────┘    │
├─────────────────────────────────────┤
│           数据访问层                 │
│  ┌─────────┬─────────┬─────────┐    │
│  │主机数据 │核查规则 │报告模板 │    │
│  └─────────┴─────────┴─────────┘    │
├─────────────────────────────────────┤
│           数据存储层                 │
│        SQLite本地数据库              │
└─────────────────────────────────────┘
```

### 3.2 目录结构
```
baseline-checker/
├── cmd/                    # 命令行入口
├── internal/
│   ├── app/               # 应用主逻辑
│   ├── config/            # 配置管理
│   ├── database/          # 数据库操作
│   ├── host/              # 主机管理
│   ├── checker/           # 核查引擎
│   ├── report/            # 报告生成
│   └── models/            # 数据模型
├── frontend/              # 前端资源
├── assets/                # 静态资源
├── templates/             # 报告模板
├── rules/                 # 核查规则
└── build/                 # 构建输出
```

## 4. 数据结构设计

### 4.1 主机数据结构
```go
type Host struct {
    ID          int64     `json:"id" db:"id"`
    Name        string    `json:"name" db:"name"`
    IP          string    `json:"ip" db:"ip"`
    OS          string    `json:"os" db:"os"`
    OSVersion   string    `json:"os_version" db:"os_version"`
    Description string    `json:"description" db:"description"`
    GroupID     int64     `json:"group_id" db:"group_id"`
    Status      string    `json:"status" db:"status"`
    CreatedAt   time.Time `json:"created_at" db:"created_at"`
    UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

type HostConnection struct {
    ID       int64  `json:"id" db:"id"`
    HostID   int64  `json:"host_id" db:"host_id"`
    Type     string `json:"type" db:"type"` // ssh, local
    Username string `json:"username" db:"username"`
    Password string `json:"password" db:"password"`
    Port     int    `json:"port" db:"port"`
    KeyPath  string `json:"key_path" db:"key_path"`
}
```

### 4.2 主机组数据结构
```go
type HostGroup struct {
    ID          int64     `json:"id" db:"id"`
    Name        string    `json:"name" db:"name"`
    Description string    `json:"description" db:"description"`
    ParentID    *int64    `json:"parent_id" db:"parent_id"`
    CreatedAt   time.Time `json:"created_at" db:"created_at"`
    UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}
```

### 4.3 核查结果数据结构
```go
type CheckResult struct {
    ID          int64     `json:"id" db:"id"`
    TaskID      int64     `json:"task_id" db:"task_id"`
    HostID      int64     `json:"host_id" db:"host_id"`
    RuleID      string    `json:"rule_id" db:"rule_id"`
    RuleName    string    `json:"rule_name" db:"rule_name"`
    Category    string    `json:"category" db:"category"`
    Level       string    `json:"level" db:"level"` // high, medium, low
    Status      string    `json:"status" db:"status"` // pass, fail, warning
    Message     string    `json:"message" db:"message"`
    Suggestion  string    `json:"suggestion" db:"suggestion"`
    Evidence    string    `json:"evidence" db:"evidence"`
    CheckedAt   time.Time `json:"checked_at" db:"checked_at"`
}
```

## 5. 界面设计

### 5.1 主界面布局
- **顶部菜单栏**: 文件、编辑、工具、帮助
- **左侧导航栏**: 主机管理、主机组、核查任务、报告中心
- **主工作区**: 动态内容显示区域
- **底部状态栏**: 任务进度、系统状态

### 5.2 主要界面
1. **主机管理界面**
   - 主机列表（表格形式）
   - 主机详情编辑
   - 连接测试功能

2. **主机组管理界面**
   - 树形结构显示
   - 拖拽组织功能
   - 批量操作

3. **核查任务界面**
   - 任务创建向导
   - 任务执行监控
   - 结果实时显示

4. **报告中心界面**
   - 报告列表
   - 报告预览
   - PDF导出功能

## 6. 开发计划

### 6.1 开发阶段
**第一阶段（4周）**: 基础框架搭建
- Wails项目初始化
- 数据库设计和实现
- 基础界面框架

**第二阶段（6周）**: 核心功能开发
- 主机管理模块
- 主机组管理模块
- Linux核查引擎

**第三阶段（4周）**: 功能完善
- Windows核查引擎
- 数据库核查引擎
- 报告生成模块

**第四阶段（2周）**: 测试和优化
- 功能测试
- 性能优化
- 文档完善

### 6.2 技术风险
- SSH连接稳定性
- 大量主机并发核查性能
- PDF报告生成复杂度
- 跨平台兼容性

## 7. 核查规则设计

### 7.1 规则分类
- **账户安全**: 用户管理、密码策略
- **权限控制**: 文件权限、服务权限
- **网络安全**: 防火墙、网络服务
- **日志审计**: 日志配置、审计策略
- **系统加固**: 系统参数、安全配置

### 7.2 规则格式
```yaml
rule_id: "LINUX_001"
name: "检查root账户登录限制"
category: "账户安全"
level: "high"
description: "检查是否禁止root用户直接登录"
command: "grep '^PermitRootLogin' /etc/ssh/sshd_config"
expected: "PermitRootLogin no"
suggestion: "建议在/etc/ssh/sshd_config中设置PermitRootLogin no"
```

## 8. 部署和维护

### 8.1 部署方式
- 单文件可执行程序
- 绿色免安装
- 自动更新机制

### 8.2 维护计划
- 核查规则定期更新
- 安全漏洞修复
- 功能迭代升级
- 用户反馈处理

---

**文档版本**: v1.0  
**创建日期**: 2025-01-20  
**最后更新**: 2025-01-20
